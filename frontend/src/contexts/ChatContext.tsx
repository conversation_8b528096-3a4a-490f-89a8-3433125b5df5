import { createContext, useContext, useEffect, useState, useRef, ReactNode } from 'react'
import { webSocketService } from '../services/websocket'
import { audioService } from '../services/audio'
import { useAuth } from './AuthContext'
import type { WebSocketMessage } from '@shared/types'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
  emotions?: Record<string, number>
  metadata?: any
}

interface ChatContextType {
  // Connection state
  isConnected: boolean
  isConnecting: boolean
  connectionError: string | null
  
  // Chat state
  isChatActive: boolean
  currentSessionId: string | null
  messages: ChatMessage[]
  
  // Audio state
  isRecording: boolean
  isPlaying: boolean
  
  // Actions
  connect: () => Promise<void>
  disconnect: () => void
  startChat: (resumedChatGroupId?: string) => void
  endChat: () => void
  sendAudioInput: (audioData: string) => void
  clearMessages: () => void
  
  // Events
  onMessage: (handler: (message: WebSocketMessage) => void) => void
  offMessage: (handler: (message: WebSocketMessage) => void) => void
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

interface ChatProviderProps {
  children: ReactNode
}

export function ChatProvider({ children }: ChatProviderProps) {
  const { user } = useAuth()

  // Connection state
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)

  // Chat state
  const [isChatActive, setIsChatActive] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])

  // Audio state
  const [isRecording, setIsRecording] = useState(false)
  const [isPlaying, setIsPlaying] = useState(false)

  // Use ref to track chat active state for immediate access in callbacks
  const isChatActiveRef = useRef(false)

  // Update recording state based on audio service
  useEffect(() => {
    const updateRecordingState = () => {
      setIsRecording(audioService.getIsRecording())
    }

    // Check recording state periodically
    const interval = setInterval(updateRecordingState, 100)

    return () => clearInterval(interval)
  }, [])

  useEffect(() => {
    if (user) {
      setupWebSocketHandlers()
    } else {
      disconnect()
    }

    return () => {
      webSocketService.disconnect()
    }
  }, [user])

  const setupWebSocketHandlers = () => {
    // Connection events
    webSocketService.on('connection_established', handleConnectionEstablished)
    webSocketService.on('connection_lost', handleConnectionLost)
    webSocketService.on('connection_error', handleConnectionError)
    
    // Chat events
    webSocketService.on('chat_started', handleChatStarted)
    webSocketService.on('chat_ended', handleChatEnded)
    webSocketService.on('hume_message', handleHumeMessage)
    webSocketService.on('error', handleError)
  }

  const handleConnectionEstablished = (_message: WebSocketMessage) => {
    console.log('✅ Chat connection established')
    setIsConnected(true)
    setIsConnecting(false)
    setConnectionError(null)
  }

  const handleConnectionLost = (_message: WebSocketMessage) => {
    console.log('🔌 Chat connection lost')
    setIsConnected(false)
    setIsConnecting(false)
    setIsChatActive(false)
    setCurrentSessionId(null)
  }

  const handleConnectionError = (message: WebSocketMessage) => {
    console.error('❌ Chat connection error:', message.data)
    setConnectionError(message.data?.error || 'Connection error')
    setIsConnecting(false)
  }

  const handleChatStarted = async (message: WebSocketMessage) => {
    console.log('🎯 Chat started:', message.data)

    // Update both state and ref immediately
    setIsChatActive(true)
    isChatActiveRef.current = true
    setCurrentSessionId(message.data?.sessionId || null)
    setMessages([])

    // Now start audio recording since chat is active
    try {
      console.log('🎤 Starting audio recording after chat started...')
      await audioService.startRecording((audioData: string) => {
        console.log('🎙️ Audio data received in callback, length:', audioData.length)
        // Use ref for immediate access to current state
        if (isChatActiveRef.current) {
          sendAudioInput(audioData)
        } else {
          console.warn('⚠️ Chat no longer active, skipping audio data')
        }
      })
      console.log('✅ Audio recording started successfully')
    } catch (error) {
      console.error('Failed to start audio recording:', error)
    }
  }

  const handleChatEnded = (message: WebSocketMessage) => {
    console.log('🏁 Chat ended:', message.data)

    // Update both state and ref immediately
    setIsChatActive(false)
    isChatActiveRef.current = false
    setCurrentSessionId(null)
    setIsRecording(false)
    setIsPlaying(false)

    // Stop audio recording
    audioService.stopRecording()
  }

  const handleHumeMessage = (message: WebSocketMessage) => {
    const humeMessage = message.data
    console.log('🤖 Hume message:', humeMessage.type)

    switch (humeMessage.type) {
      case 'user_message':
        addMessage({
          id: humeMessage.message?.id || Date.now().toString(),
          role: 'user',
          content: humeMessage.message?.content || '',
          timestamp: new Date(humeMessage.timestamp || Date.now()),
          emotions: extractEmotions(humeMessage),
          metadata: humeMessage
        })
        setIsRecording(false)
        // Stop audio playback when user speaks
        audioService.stopPlayback()
        break

      case 'assistant_message':
        addMessage({
          id: humeMessage.message?.id || Date.now().toString(),
          role: 'assistant',
          content: humeMessage.message?.content || '',
          timestamp: new Date(humeMessage.timestamp || Date.now()),
          metadata: humeMessage
        })
        break

      case 'audio_output':
        setIsPlaying(true)
        // Play audio using the audio service
        audioService.playAudio(humeMessage)
        break

      case 'user_interruption':
        setIsPlaying(false)
        // Stop audio playback when user interrupts
        audioService.stopPlayback()
        break

      case 'tool_call':
        addMessage({
          id: Date.now().toString(),
          role: 'assistant',
          content: `[Tool Call: ${humeMessage.name}]`,
          timestamp: new Date(),
          metadata: humeMessage
        })
        break
    }
  }

  const handleError = (message: WebSocketMessage) => {
    console.error('💥 Chat error:', message.data)
    setConnectionError(message.data?.message || 'An error occurred')
  }

  const extractEmotions = (humeMessage: any): Record<string, number> => {
    const emotions: Record<string, number> = {}
    
    if (humeMessage.models?.prosody?.scores) {
      Object.entries(humeMessage.models.prosody.scores).forEach(([emotion, score]) => {
        emotions[emotion] = score as number
      })
    }
    
    return emotions
  }

  const addMessage = (message: ChatMessage) => {
    setMessages(prev => [...prev, message])
  }

  const connect = async () => {
    if (!user) {
      throw new Error('User must be authenticated to connect')
    }

    try {
      setIsConnecting(true)
      setConnectionError(null)
      await webSocketService.connect()
    } catch (error) {
      console.error('Failed to connect to chat:', error)
      setConnectionError(error instanceof Error ? error.message : 'Connection failed')
      setIsConnecting(false)
      throw error
    }
  }

  const disconnect = () => {
    webSocketService.disconnect()
    setIsConnected(false)
    setIsConnecting(false)
    setIsChatActive(false)
    isChatActiveRef.current = false
    setCurrentSessionId(null)
    setMessages([])
    setIsRecording(false)
    setIsPlaying(false)
  }

  const startChat = async (resumedChatGroupId?: string) => {
    if (!isConnected) {
      throw new Error('Not connected to chat server')
    }

    try {
      console.log('🎬 Starting chat with audio...')

      // Initialize audio service (but don't start recording yet)
      console.log('🎵 Initializing audio service...')
      await audioService.init()

      // Start chat session - audio recording will start when chat_started is received
      console.log('💬 Starting WebSocket chat session...')
      webSocketService.startChat(resumedChatGroupId)
    } catch (error) {
      console.error('Failed to start chat with audio:', error)
      throw error
    }
  }

  const endChat = () => {
    if (isChatActive) {
      // Stop audio recording
      audioService.stopRecording()

      // End chat session
      webSocketService.endChat()
    }
  }

  const sendAudioInput = (audioData: string) => {
    console.log('🎙️ ChatContext sendAudioInput called, data length:', audioData.length)
    console.log('🎙️ Chat state:', {
      isConnected,
      isChatActive: isChatActiveRef.current,
      currentSessionId,
      wsConnectionState: webSocketService.connectionState
    })

    if (!isConnected) {
      console.error('❌ WebSocket not connected')
      throw new Error('WebSocket not connected')
    }

    if (!isChatActiveRef.current) {
      console.error('❌ No active chat session for audio input')
      throw new Error('No active chat session')
    }

    webSocketService.sendAudioInput(audioData)
  }

  const clearMessages = () => {
    setMessages([])
  }

  const onMessage = (handler: (message: WebSocketMessage) => void) => {
    webSocketService.on('*', handler)
  }

  const offMessage = (handler: (message: WebSocketMessage) => void) => {
    webSocketService.off('*', handler)
  }

  const value: ChatContextType = {
    // Connection state
    isConnected,
    isConnecting,
    connectionError,
    
    // Chat state
    isChatActive,
    currentSessionId,
    messages,
    
    // Audio state
    isRecording,
    isPlaying,
    
    // Actions
    connect,
    disconnect,
    startChat,
    endChat,
    sendAudioInput,
    clearMessages,
    
    // Events
    onMessage,
    offMessage
  }

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  )
}

export function useChat() {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  return context
}
